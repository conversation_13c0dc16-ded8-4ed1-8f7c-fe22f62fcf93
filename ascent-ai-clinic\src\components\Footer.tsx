
import { Button } from "@/components/ui/button";
import { <PERSON>R<PERSON>, MapPin, Mail, Phone } from "lucide-react";
import { Link } from "react-router-dom";
import { useIndustry, useIndustryContext } from "@/contexts/IndustryContext";

const Footer = () => {
  // Use industry context directly
  const { industry } = useIndustry();
  const { isClinic, isHotel } = useIndustryContext();

  // Get industry-specific Book Call URL
  const getBookCallUrl = () => {
    if (industry === 'clinic') return "/clinic/book-call";
    if (industry === 'hotel') return "/hotel/book-call";
    return "/book-call"; // fallback for general
  };

  // Footer pattern helpers
  const getFooterConfig = () => {
    if (isClinic) {
      return {
        title: "Ready to Transform Your Clinic?",
        description: "Book your free strategy call today and discover how AI can revolutionize your clinic operations.",
        aboutText: "Your dedicated partner in healthcare innovation, delivering custom AI solutions that transform clinic operations and improve patient care.",
        ctaText: "Book Your Free Strategy Call",
      };
    } else if (isHotel) {
      return {
        title: "Ready to Transform Your Hotel?",
        description: "Book your free AI strategy session today and discover how we can revolutionize your hotel operations.",
        aboutText: "Your dedicated partner in hotel innovation, delivering custom AI solutions that transform operations and enhance guest experiences.",
        ctaText: "Book Your Free AI Strategy Session",
      };
    } else {
      return {
        title: "Ready to Transform Your Business?",
        description: "Book your free strategy call today and discover how AI can revolutionize your operations.",
        aboutText: "Your dedicated partner in business innovation, delivering custom AI solutions that transform operations across industries.",
        ctaText: "Book Your Free Strategy Call",
      };
    }
  };

  const { title, description, aboutText, ctaText } = getFooterConfig();

  return (
    <footer className="bg-gray-900 text-white py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA Section */}
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              {title}
            </h2>

            <p className="text-xl text-gray-300 mb-8">
              {description}
            </p>

            <Button
              asChild
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <Link to={getBookCallUrl()}>
                {ctaText}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>

          {/* Footer Content Grid */}
          <div className="border-t border-gray-700 pt-12">
            <div className="grid md:grid-cols-2 gap-12 mb-8">
              {/* Contact Us */}
              <div>
                <h3 className="text-xl font-semibold mb-4">Contact Us</h3>
                <div className="space-y-4">
                  {/* Singapore Office */}
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <MapPin className="w-5 h-5 text-blue-400 flex-shrink-0" />
                      <span className="text-gray-300 font-medium">Singapore</span>
                    </div>
                    <div className="ml-8 space-y-1">
                      <div className="flex items-center gap-3">
                        <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        <a
                          href="mailto:<EMAIL>"
                          className="text-gray-300 hover:text-blue-400 transition-colors text-sm"
                        >
                          <EMAIL>
                        </a>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        <a
                          href="tel:+6590032175"
                          className="text-gray-300 hover:text-blue-400 transition-colors text-sm"
                        >
                          +65 9003 2175
                        </a>
                      </div>
                    </div>
                  </div>

                  {/* Paris Office */}
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <MapPin className="w-5 h-5 text-blue-400 flex-shrink-0" />
                      <span className="text-gray-300 font-medium">Paris</span>
                    </div>
                    <div className="ml-8 space-y-1">
                      <div className="flex items-center gap-3">
                        <Mail className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        <a
                          href="mailto:<EMAIL>"
                          className="text-gray-300 hover:text-blue-400 transition-colors text-sm"
                        >
                          <EMAIL>
                        </a>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        <a
                          href="tel:+33767199919"
                          className="text-gray-300 hover:text-blue-400 transition-colors text-sm"
                        >
                          +33 7 67 19 99 19
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* About Ascent AI */}
              <div>
                <h3 className="text-xl font-semibold mb-4">About Ascent AI</h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  {aboutText}
                </p>

                {/* Copyright */}
                <div className="text-gray-400 text-sm">
                  © 2025 Ascent AI. All rights reserved.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
