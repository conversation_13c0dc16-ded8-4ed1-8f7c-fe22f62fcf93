
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { ArrowRight, Menu } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { useIndustry } from "@/contexts/IndustryContext";

const Header = () => {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const { industry, branding, navigation } = useIndustry();

  const closeSheet = () => setIsOpen(false);

  // Get industry-specific Book Call URL
  const getBookCallUrl = () => {
    if (industry === 'clinic') return "/clinic/book-call";
    if (industry === 'hotel') return "/hotel/book-call";
    return "/book-call"; // fallback for general
  };

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center">
            <h1 className="text-2xl font-bold text-blue-600">
              {branding.name}
            </h1>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`text-sm font-medium transition-colors hover:text-blue-600 ${
                  location.pathname === item.path
                    ? "text-blue-600"
                    : "text-gray-600"
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>
          
          {/* Desktop Book Call Button */}
          <Button 
            asChild
            size="sm"
            className="hidden md:inline-flex bg-blue-600 hover:bg-blue-700 text-white group"
          >
            <Link to={getBookCallUrl()}>
              Book Call
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </Button>

          {/* Mobile Burger Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <nav className="flex flex-col space-y-6 mt-6">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.path}
                    onClick={closeSheet}
                    className={`text-lg font-medium transition-colors hover:text-blue-600 ${
                      location.pathname === item.path
                        ? "text-blue-600"
                        : "text-gray-600"
                    }`}
                  >
                    {item.name}
                  </Link>
                ))}
                <Button 
                  asChild
                  className="bg-blue-600 hover:bg-blue-700 text-white group mt-4"
                >
                  <Link to={getBookCallUrl()} onClick={closeSheet}>
                    Book Call
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Header;
