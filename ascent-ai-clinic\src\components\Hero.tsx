
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { useIndustry } from "@/contexts/IndustryContext";

const Hero = () => {
  const { industry } = useIndustry();

  // Get industry-specific Book Call URL
  const getBookCallUrl = () => {
    if (industry === 'clinic') return "/clinic/book-call";
    if (industry === 'hotel') return "/hotel/book-call";
    return "/book-call"; // fallback for general
  };
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
            AI That Runs Your Clinic
            <span className="block text-blue-600">So You Don't Have To</span>
          </h1>
          
          <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
            Reduce overheads, book more appointments, and eliminate admin chaos — all without hiring more staff.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link to={getBookCallUrl()}>
              <Button size="lg" className="w-full sm:w-auto">
                Book a Call
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
      
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
      </div>
    </section>
  );
};

export default Hero;
