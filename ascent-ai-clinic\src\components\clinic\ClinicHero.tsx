import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const ClinicHero = () => {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
            AI That Runs Your Clinic
            <span className="block text-blue-600">So You Don't Have To</span>
          </h1>
          
          <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
            Reduce overheads, book more appointments, and eliminate admin chaos with custom AI solutions for your healthcare clinic.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/clinic/book-call">
              <Button size="lg" className="w-full sm:w-auto">
                Book a Free Strategy Call
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClinicHero;