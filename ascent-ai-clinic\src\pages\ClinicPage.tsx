import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, CheckCircle, Clock, TrendingUp, DollarSign } from "lucide-react";
import { Link } from "react-router-dom";

const ClinicPage = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              AI That Runs Your Clinic
              <span className="block text-blue-600">So You Don't Have To</span>
            </h1>

            <p className="text-xl sm:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Reduce overheads, book more appointments, and eliminate admin chaos — all without hiring more staff.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button asChild size="lg" className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700">
                <Link to="/clinic/book-call">
                  ➡️ Book Your Free Strategy Call
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Running a Clinic Shouldn't Feel Like Running on a Treadmill
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              If you're a clinic owner, chances are you're juggling missed appointments and time-consuming admin work, and you're definitely not the only one.
            </p>
          </div>

          <div className="max-w-5xl mx-auto space-y-6 mb-12">
            <div className="bg-red-50 border-l-4 border-red-500 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-red-800 mb-2">Missed or Unconfirmed Appointments (No-Shows)</h3>
              <p className="text-red-700">Due to poor follow-ups and lack of reminders.</p>
            </div>

            <div className="bg-orange-50 border-l-4 border-orange-500 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-orange-800 mb-2">Inefficient Front Desk Operations</h3>
              <p className="text-orange-700">Caused by outdated systems, lack of automation, and fragmented workflows often lead admin staff to spend hours on repetitive tasks.</p>
            </div>

            <div className="bg-yellow-50 border-l-4 border-yellow-500 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">Poor Patient Follow-up</h3>
              <p className="text-yellow-700">Due to the absence of a dedicated system to capture, qualify, and convert new inquiries leads to missed opportunities and lost revenue.</p>
            </div>

            <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">Underutilised Time Slots</h3>
              <p className="text-blue-700">Caused by the absence of real-time scheduling intelligence or dynamic rebooking lead to lost billable hours and decreased efficiency.</p>
            </div>

            <div className="bg-purple-50 border-l-4 border-purple-500 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-purple-800 mb-2">High Admin Overhead</h3>
              <p className="text-purple-700">From hiring extra staff due to inefficient systems for forms, documentation, and reminders.</p>
            </div>
          </div>

          <div className="text-center">
            <p className="text-xl font-semibold text-foreground">
              Does this sound familiar to you?
            </p>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-20 bg-blue-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Automate. Optimise. Grow.
            </h2>
            <p className="text-lg text-muted-foreground">
              We build custom AI systems that take care of the boring, repetitive tasks — so your team can focus on care, not clerical work.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            <div className="flex items-start gap-4 bg-white p-6 rounded-lg shadow-sm">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Automate appointment booking & reminders</h3>
              </div>
            </div>

            <div className="flex items-start gap-4 bg-white p-6 rounded-lg shadow-sm">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Instant responses to inquiries via AI agents</h3>
              </div>
            </div>

            <div className="flex items-start gap-4 bg-white p-6 rounded-lg shadow-sm">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Reactivation of Inactive Patients</h3>
              </div>
            </div>

            <div className="flex items-start gap-4 bg-white p-6 rounded-lg shadow-sm">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Reduce human error and missed follow-ups</h3>
              </div>
            </div>

            <div className="flex items-start gap-4 bg-white p-6 rounded-lg shadow-sm">
              <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Slash your admin hours by up to 80%</h3>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Real Clinics. Real Results.
            </h2>
          </div>

          <div className="max-w-4xl mx-auto mb-16">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-xl">
              <blockquote className="text-xl text-muted-foreground mb-6 italic">
                "We automated appointment confirmations and follow-ups. No more manual chasing — and our no-show rate dropped by 38% in 2 months."
              </blockquote>
              <p className="font-semibold text-foreground">
                – Dr. Tan, Family Clinic (SG)
              </p>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-8">
            <div className="text-center bg-green-50 p-6 rounded-lg">
              <TrendingUp className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-green-800 mb-2">2x increase</h3>
              <p className="text-green-700">in confirmed bookings</p>
            </div>

            <div className="text-center bg-blue-50 p-6 rounded-lg">
              <DollarSign className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-blue-800 mb-2">$40k+ saved</h3>
              <p className="text-blue-700">yearly in admin costs</p>
            </div>

            <div className="text-center bg-purple-50 p-6 rounded-lg">
              <Clock className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-purple-800 mb-2">20+ hours/week</h3>
              <p className="text-purple-700">saved for front desk staff</p>
            </div>
          </div>

          <div className="text-center">
            <p className="text-sm text-muted-foreground italic">
              *Results vary based on clinic size and service model.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Here's How We Transform Your Clinic in 30 Days
            </h2>
          </div>

          <div className="max-w-4xl mx-auto space-y-12">
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                1
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Research</h3>
                <p className="text-lg text-muted-foreground">
                  Free strategy call where we dive into your workflow bottlenecks.
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-green-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                2
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Build</h3>
                <p className="text-lg text-muted-foreground">
                  Our team builds tailored solutions that solve your pain points.
                </p>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="flex-shrink-0 w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center text-2xl font-bold">
                3
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-3">Analyse</h3>
                <p className="text-lg text-muted-foreground">
                  Monthly check-ins to ensure real-world effectiveness and optimise performance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
                About Ascent AI
              </h2>
            </div>

            <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
              <p>
                At <strong className="text-foreground">Ascent AI</strong>, we help private healthcare clinics work smarter, reduce costs, and grow faster through custom-built AI solutions.
              </p>

              <p>
                We are a newly founded team of highly skilled full-stack AI developers who can create anything you envision. Whether you need to automate repetitive admin tasks, streamline patient communications, or increase appointment bookings, our team can build it with precision and speed. If you can think it, we can build it.
              </p>

              <p>
                Our focus is on solving the real problems clinic owners face daily, from high overhead costs and inefficient workflows to missed revenue opportunities. We specialise in crafting intelligent systems that automate the routine, improve operational efficiency, and free your team to focus on delivering better care.
              </p>

              <p>
                What sets us apart is not just our technical expertise, but our hands-on, collaborative approach. We guide you from concept to execution, building tools that integrate seamlessly into your clinic's workflow and deliver measurable results.
              </p>

              <p className="font-semibold text-blue-600">
                At Ascent AI, we believe technology should work for you, not the other way around. We're here to help you unlock the full potential of your clinic with AI that actually makes a difference.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="max-w-4xl mx-auto space-y-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">Is it safe to use AI in a clinic setting?</h3>
              <p className="text-muted-foreground">
                Yes. We fully comply with healthcare regulations and ensure all patient data is kept secure and confidential.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">Will this replace my existing staff?</h3>
              <p className="text-muted-foreground">
                No. Our goal is to empower your team, not replace them. By automating repetitive tasks, your staff can focus on higher-value activities like patient care, improving efficiency and morale.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">How quickly can we get started?</h3>
              <p className="text-muted-foreground">
                We can begin with a free strategy call first. Most clinics go live within 2 to 4 weeks, depending on the complexity of the solution needed.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">What kind of tasks can you automate?</h3>
              <p className="text-muted-foreground">
                We can automate appointment booking, patient follow-ups, reminders, data entry, form filling, internal task assignments, and even responses to common inquiries through AI chat agents or phone bots.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">Do you integrate with our existing clinic systems?</h3>
              <p className="text-muted-foreground">
                Yes. During onboarding, we assess your current systems and build solutions that seamlessly integrate with your electronic medical records (EMR) platforms, including Plato, as well as your scheduling tools.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">What happens if something breaks or needs support?</h3>
              <p className="text-muted-foreground">
                We provide ongoing support and maintenance. If anything goes wrong or needs adjusting, our team is on standby to fix issues quickly and keep everything running smoothly.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">How much does it cost?</h3>
              <p className="text-muted-foreground">
                Pricing depends on the complexity of the solution and your clinic's needs. We offer flexible packages designed to deliver a return on investment through cost savings, time efficiency, and increased revenue.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">Do I need to be tech-savvy to use this?</h3>
              <p className="text-muted-foreground">
                Not at all. Our development team handles all the technical work for you, so you don't have to learn anything yourself.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold mb-3">Can this work for multi-location clinics?</h3>
              <p className="text-muted-foreground">
                Yes. Our AI solutions are scalable and can support both single-location and multi-site operations with central oversight and reporting.
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ClinicPage;