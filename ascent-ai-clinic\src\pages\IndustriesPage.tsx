import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Footer from "@/components/Footer";

const IndustriesPage = () => {
  return (
    <div className="min-h-screen bg-background">
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">
              Our Industry Solutions
            </h1>
            
            <p className="text-lg text-muted-foreground mb-12">
              Discover how Ascent AI can transform operations across different industries.
            </p>
          </div>
        </div>
      </section>
      
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-8 rounded-lg shadow-sm">
              <h3 className="text-2xl font-semibold mb-4">Healthcare Clinics</h3>
              <p className="mb-6 text-gray-600">
                AI that runs your clinic so you don't have to. Reduce overheads, book more appointments, and eliminate admin chaos.
              </p>
              <div className="flex justify-between items-center">
                <Link to="/clinic" className="text-blue-600 hover:underline font-medium">
                  Learn more →
                </Link>
                <Button asChild size="sm" variant="outline">
                  <Link to="/clinic/book-call">
                    Book Call
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
            
            <div className="bg-gray-100 p-8 rounded-lg shadow-sm">
              <h3 className="text-2xl font-semibold mb-4">Coming Soon</h3>
              <p className="mb-6 text-gray-600">
                More industry-specific AI solutions are in development. Stay tuned for updates or contact us to discuss your industry needs.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Available soon</span>
                <Button asChild size="sm" variant="outline">
                  <Link to="/book-call">
                    Contact Us
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default IndustriesPage;